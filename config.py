# Configuration file for the FastAPI application

# Valid client UUIDs for authentication
# Add your valid client UUIDs here
VALID_CLIENT_KEYS = {
    "550e8400-e29b-41d4-a716-************",  # Example UUID - replace with actual client keys
    # Add more valid UUIDs as needed
    # "123e4567-e89b-12d3-a456-************",
}

# Data directory configuration
if os.getenv('RAILWAY_ENVIRONMENT_NAME') == 'production':
    DATA_DIRECTORY = "../data"
else:
    DATA_DIRECTORY = "./data"

# Other configuration options can be added here
DEBUG_MODE = False
